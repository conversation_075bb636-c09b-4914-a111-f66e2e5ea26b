{"rustc": 1842507548689473721, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2040997289075261528, "path": 9519017829329005796, "deps": [[5230392855116717286, "equivalent", false, 6804970669098500258], [9150530836556604396, "allocator_api2", false, 17023579432767426380], [10842263908529601448, "<PERSON><PERSON><PERSON>", false, 16461026173464783458]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-8bae4ea509bebfad\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}