{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2040997289075261528, "path": 14264657120815013845, "deps": [[784494742817713399, "tower_service", false, 3577325964247015323], [1906322745568073236, "pin_project_lite", false, 4230724718521225101], [4121350475192885151, "iri_string", false, 13056164317276796369], [5695049318159433696, "tower", false, 8757218926948728662], [7712452662827335977, "tower_layer", false, 18396764942463774731], [7896293946984509699, "bitflags", false, 15739841351032332181], [9010263965687315507, "http", false, 9320379225202554334], [10629569228670356391, "futures_util", false, 12233556254897263712], [14084095096285906100, "http_body", false, 15495320847413964727], [16066129441945555748, "bytes", false, 13415992542601879811]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tower-http-f98b571ed00aa99d\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}