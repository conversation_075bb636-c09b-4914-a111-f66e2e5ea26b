{"rustc": 1842507548689473721, "features": "[\"default\", \"runtime-detection\", \"serde\", \"serde_impl\", \"serde_json\", \"swar-number-parsing\"]", "declared_features": "[\"128bit\", \"ahash\", \"alloc\", \"alloc_counter\", \"approx-number-parsing\", \"arraybackend\", \"beef\", \"bench-all\", \"bench-apache_builds\", \"bench-canada\", \"bench-citm_catalog\", \"bench-event_stacktrace_10kb\", \"bench-github_events\", \"bench-log\", \"bench-serde\", \"bench-twitter\", \"big-int-as-float\", \"colored\", \"default\", \"docsrs\", \"getopts\", \"hints\", \"jemallocator\", \"known-key\", \"no-inline\", \"once_cell\", \"ordered-float\", \"perf\", \"perfcnt\", \"runtime-detection\", \"serde\", \"serde_impl\", \"serde_json\", \"swar-number-parsing\", \"value-no-dup-keys\"]", "target": 731617435406095498, "profile": 6818150329730343533, "path": 7937962435338027893, "deps": [[7018549409914685819, "halfbrown", false, 12738444667582938026], [7051503050471985935, "value_trait", false, 13061192050075460187], [8067010153367330186, "simdutf8", false, 8625676770039469394], [9689903380558560274, "serde", false, 17270949286662840186], [13535282280064720520, "ref_cast", false, 18373757999533302120], [16362055519698394275, "serde_json", false, 11308209756294985618]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\simd-json-15a6bb1495396151\\dep-lib-simd_json", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}