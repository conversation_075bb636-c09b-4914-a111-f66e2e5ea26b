use serde_json;

/// 改进的JSON解析器，专门处理AI返回的可能有问题的JSON
pub struct ImprovedJsonParser;

impl ImprovedJsonParser {
    /// 解析AI返回的JSON响应
    pub fn parse_ai_response(response_text: &str) -> Result<serde_json::Value, String> {
        println!("🔍 开始解析AI响应，长度: {} 字符", response_text.len());
        
        // 显示原始响应的前100个字符用于调试
        let preview = Self::safe_preview(response_text, 100);
        println!("🔍 AI Response preview: {}", preview);
        
        // 第一步：基本清理
        let cleaned = Self::basic_clean(response_text);
        println!("🧹 基本清理后长度: {} 字符", cleaned.len());
        
        // 尝试解析基本清理后的JSON
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&cleaned) {
            println!("✅ 基本清理后解析成功");
            return Ok(parsed);
        }
        
        // 第二步：修复字符串中的换行符
        let fixed_newlines = Self::fix_json_newlines(&cleaned);
        println!("🔧 修复换行符后长度: {} 字符", fixed_newlines.len());
        
        // 尝试解析修复换行符后的JSON
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&fixed_newlines) {
            println!("✅ 修复换行符后解析成功");
            return Ok(parsed);
        }
        
        // 第三步：激进清理
        let ultra_cleaned = Self::ultra_clean(&fixed_newlines);
        println!("🔨 激进清理后长度: {} 字符", ultra_cleaned.len());
        
        // 尝试解析激进清理后的JSON
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&ultra_cleaned) {
            println!("✅ 激进清理后解析成功");
            return Ok(parsed);
        }
        
        // 第四步：尝试修复常见的JSON格式问题
        let format_fixed = Self::fix_json_format(&ultra_cleaned);
        println!("🛠️ 格式修复后长度: {} 字符", format_fixed.len());
        
        // 最后尝试
        match serde_json::from_str::<serde_json::Value>(&format_fixed) {
            Ok(parsed) => {
                println!("✅ 格式修复后解析成功");
                Ok(parsed)
            }
            Err(e) => {
                println!("❌ 所有尝试都失败了");
                println!("最终错误: {}", e);
                
                // 显示错误位置的详细信息
                Self::show_error_details(&e, &format_fixed);
                
                Err(format!("JSON解析失败: {}", e))
            }
        }
    }
    
    /// 安全地生成预览文本，避免字符边界问题
    fn safe_preview(text: &str, max_len: usize) -> String {
        if text.len() <= max_len {
            return text.to_string();
        }
        
        let mut end = max_len;
        while end > 0 && !text.is_char_boundary(end) {
            end -= 1;
        }
        
        format!("{}...", &text[..end])
    }
    
    /// 基本清理：移除markdown标记和BOM
    fn basic_clean(text: &str) -> String {
        let mut cleaned = text.trim().to_string();
        
        // 移除markdown代码块标记
        if cleaned.starts_with("```json") {
            cleaned = cleaned.replace("```json", "").replace("```", "").trim().to_string();
        } else if cleaned.starts_with("```") {
            cleaned = cleaned.replace("```", "").trim().to_string();
        }
        
        // 移除BOM
        if cleaned.starts_with('\u{FEFF}') {
            cleaned = cleaned.trim_start_matches('\u{FEFF}').to_string();
        }
        
        cleaned
    }
    
    /// 修复JSON字符串中的换行符问题
    fn fix_json_newlines(text: &str) -> String {
        let mut result = String::new();
        let mut in_string = false;
        let mut escape_next = false;
        
        for c in text.chars() {
            if escape_next {
                result.push(c);
                escape_next = false;
                continue;
            }
            
            match c {
                '\\' => {
                    result.push(c);
                    escape_next = true;
                }
                '"' => {
                    result.push(c);
                    in_string = !in_string;
                }
                '\n' if in_string => {
                    // 在字符串内的换行符需要转义
                    result.push_str("\\n");
                }
                '\r' if in_string => {
                    // 在字符串内的回车符需要转义
                    result.push_str("\\r");
                }
                '\t' if in_string => {
                    // 在字符串内的制表符需要转义
                    result.push_str("\\t");
                }
                _ => {
                    result.push(c);
                }
            }
        }
        
        result
    }
    
    /// 激进清理：移除所有控制字符
    fn ultra_clean(text: &str) -> String {
        text.chars()
            .filter(|&c| {
                match c {
                    // 保留JSON结构字符
                    '{' | '}' | '[' | ']' | '"' | ':' | ',' | ' ' => true,
                    // 保留基本空白字符
                    '\n' | '\t' => true,
                    // 保留可打印ASCII字符
                    c if c.is_ascii() && !c.is_control() => true,
                    // 保留中文等非ASCII字符
                    c if !c.is_ascii() && !c.is_control() => true,
                    // 移除其他控制字符
                    _ => false,
                }
            })
            .collect()
    }
    
    /// 修复常见的JSON格式问题
    fn fix_json_format(text: &str) -> String {
        let mut fixed = text.to_string();
        
        // 确保JSON对象正确闭合
        let open_braces = fixed.matches('{').count();
        let close_braces = fixed.matches('}').count();
        
        if open_braces > close_braces {
            for _ in 0..(open_braces - close_braces) {
                fixed.push('}');
            }
        }
        
        // 移除多余的逗号
        fixed = fixed.replace(",}", "}");
        fixed = fixed.replace(",]", "]");
        
        // 修复可能的双引号问题
        fixed = Self::fix_quotes(&fixed);
        
        fixed
    }
    
    /// 修复引号问题
    fn fix_quotes(text: &str) -> String {
        let mut result = String::new();
        let mut in_string = false;
        let mut escape_next = false;
        
        for c in text.chars() {
            if escape_next {
                result.push(c);
                escape_next = false;
                continue;
            }
            
            match c {
                '\\' => {
                    result.push(c);
                    escape_next = true;
                }
                '"' => {
                    result.push(c);
                    in_string = !in_string;
                }
                _ => {
                    result.push(c);
                }
            }
        }
        
        // 如果字符串没有正确闭合，添加闭合引号
        if in_string {
            result.push('"');
        }
        
        result
    }
    
    /// 显示错误详细信息
    fn show_error_details(error: &serde_json::Error, text: &str) {
        if let Some(line) = error.line().checked_sub(1) {
            let lines: Vec<&str> = text.lines().collect();
            if line < lines.len() {
                println!("📍 错误位置: 第{}行", line + 1);
                println!("📄 错误行内容: {}", lines[line]);
                
                // 分析错误行的字符
                println!("🔍 错误行字符分析:");
                for (i, c) in lines[line].chars().enumerate().take(20) {
                    if c.is_control() {
                        println!("    位置 {}: U+{:04X} (控制字符)", i, c as u32);
                    }
                }
            }
        }
    }
}
