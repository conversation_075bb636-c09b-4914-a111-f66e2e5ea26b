{"rustc": 1842507548689473721, "features": "[\"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"stream\", \"tokio-native-tls\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"url\", \"webpki-roots\"]", "target": 10194999948271016277, "profile": 2040997289075261528, "path": 3942086151908485922, "deps": [[4254813506600451364, "tungstenite", false, 13610767174458289701], [5986029879202738730, "log", false, 4268188471851143901], [10629569228670356391, "futures_util", false, 12233556254897263712], [12186126227181294540, "tokio_native_tls", false, 14140902317976038864], [16785601910559813697, "native_tls_crate", false, 10715414495494162563], [17531218394775549125, "tokio", false, 8622598028916719679]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tokio-tungstenite-3db4c469b396f688\\dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}