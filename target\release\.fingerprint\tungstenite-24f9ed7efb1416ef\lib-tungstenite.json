{"rustc": 1842507548689473721, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 2040997289075261528, "path": 10234824174975898059, "deps": [[99287295355353247, "data_encoding", false, 11173884700876300933], [4359956005902820838, "utf8", false, 12122377330052704261], [5986029879202738730, "log", false, 4268188471851143901], [6163892036024256188, "httparse", false, 2643518890952457947], [9010263965687315507, "http", false, 9320379225202554334], [10724389056617919257, "sha1", false, 15736734959870524346], [10806645703491011684, "thiserror", false, 9353345601066647366], [11916940916964035392, "rand", false, 1569429620821156706], [16066129441945555748, "bytes", false, 13415992542601879811], [16785601910559813697, "native_tls_crate", false, 10715414495494162563]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tungstenite-24f9ed7efb1416ef\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}