[package]
name = "BinanceWebsocketNotifier"
version = "8.8.8"
edition = "2024"


[dependencies]
chrono = { version = "0.4.41", features = ["serde"] }
dotenvy = "0.15.7"
env_logger = "0.11.8"
futures-util = "0.3.31"
hex = "0.4.3"
hmac = "0.12.1"
reqwest = { version = "0.12.22", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.142"
sha2 = "0.10.9"
thiserror = "2.0.12"
tokio = { version = "1.47.1", features = ["full", "signal"] }
tokio-tungstenite = { version = "0.27.0", features = ["native-tls"] }
url = "2.5.4"


