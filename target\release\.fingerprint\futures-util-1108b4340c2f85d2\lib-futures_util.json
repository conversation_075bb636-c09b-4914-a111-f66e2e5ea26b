{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"futures-sink\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 11030844300562395373, "deps": [[1615478164327904835, "pin_utils", false, 10190762824614676676], [1906322745568073236, "pin_project_lite", false, 4230724718521225101], [5451793922601807560, "slab", false, 15446050799820957315], [7013762810557009322, "futures_sink", false, 7144979372798496277], [7620660491849607393, "futures_core", false, 5371940579528272629], [10565019901765856648, "futures_macro", false, 11281865324900513526], [16240732885093539806, "futures_task", false, 13238839052769586432]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-util-1108b4340c2f85d2\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}