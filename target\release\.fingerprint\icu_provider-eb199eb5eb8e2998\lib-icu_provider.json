{"rustc": 1842507548689473721, "features": "[\"baked\", \"zerotrie\"]", "declared_features": "[\"alloc\", \"baked\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"export\", \"logging\", \"serde\", \"std\", \"sync\", \"zerotrie\"]", "target": 8134314816311233441, "profile": 2040997289075261528, "path": *******************, "deps": [[577007972892873560, "icu_locale_core", false, 5379184183522212530], [1720717020211068583, "writeable", false, 13728197431251768676], [2094002304596326048, "zerotrie", false, 6895602035093682172], [*******************, "stable_deref_trait", false, 14387663927227905146], [5298260564258778412, "displaydoc", false, 8428700490151738812], [6120213164091718614, "zerovec", false, 1856582370854031175], [10706449961930108323, "yoke", false, 13694906078268831705], [17046516144589451410, "zerofrom", false, 6770533640350317807], [18328566729972757851, "tinystr", false, 7083236493467118219]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\icu_provider-eb199eb5eb8e2998\\dep-lib-icu_provider", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}