use std::env;
use tokio::sync::broadcast;
use reqwest::Client;
use serde_json::json;
use chrono::{DateTime, Utc, FixedOffset};

use crate::types::{RawAnnouncement, TranslatedAnnouncement, Message, Result, AppError};
use crate::json_parser::ImprovedJsonParser;

/// 翻译和总结结果
#[derive(Debug)]
struct TranslationResult {
    translated_title: String,
    translated_body: String,
    summary: String,
}

pub struct AITranslator {
    client: Client,
    api_key: String,
    api_base_url: String,
    model: String,
}

impl AITranslator {
    pub fn new() -> Result<Self> {
        let api_key = env::var("OPENROUTER_API_KEY")
            .map_err(|_| AppError::Config("OPENROUTER_API_KEY not found in environment".to_string()))?;

        let api_base_url = "https://openrouter.ai/api/v1".to_string();
        
        let model = env::var("TRANSLATION_MODEL")
            .unwrap_or_else(|_| "google/gemini-2.5-flash-lite-preview-06-17".to_string());

        Ok(Self {
            client: Client::new(),
            api_key,
            api_base_url,
            model,
        })
    }

    /// 运行 AI 翻译器
    pub async fn run(
        &self,
        mut message_rx: broadcast::Receiver<Message>,
        message_tx: broadcast::Sender<Message>,
        mut shutdown_rx: broadcast::Receiver<()>,
    ) -> Result<()> {
        println!("🤖 AI Translator started");

        loop {
            tokio::select! {
                // 接收原始公告消息
                msg = message_rx.recv() => {
                    match msg {
                        Ok(Message::RawAnnouncement(raw_announcement)) => {
                            println!("🤖 Received announcement for translation: {}", raw_announcement.title);
                            
                            match self.translate_announcement(raw_announcement).await {
                                Ok(translated) => {
                                    println!("✅ Translation completed: {}", translated.translated_title);
                                    
                                    if let Err(e) = message_tx.send(Message::TranslatedAnnouncement(translated)) {
                                        eprintln!("❌ Failed to send translated announcement: {}", e);
                                    }
                                }
                                Err(e) => {
                                    eprintln!("❌ Translation failed: {}", e);
                                }
                            }
                        }
                        Ok(Message::Shutdown) => {
                            println!("🛑 AI Translator received shutdown signal");
                            break;
                        }
                        Ok(_) => {
                            // 忽略其他消息类型
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            eprintln!("⚠️ AI Translator lagged, skipped {} messages", skipped);
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            println!("📡 Message channel closed, shutting down AI Translator");
                            break;
                        }
                    }
                }
                // 处理关闭信号
                _ = shutdown_rx.recv() => {
                    println!("🛑 AI Translator shutdown signal received");
                    break;
                }
            }
        }

        println!("👋 AI Translator shutdown complete");
        Ok(())
    }

    /// 翻译公告
    async fn translate_announcement(&self, raw: RawAnnouncement) -> Result<TranslatedAnnouncement> {
        println!("🔄 Translating announcement: {}", raw.title);

        // 调用AI API进行翻译
        let translation_result = self.translate_and_summarize(&raw.title, &raw.body).await?;

        let translated = TranslatedAnnouncement {
            original_title: raw.title.clone(),
            translated_title: translation_result.translated_title,
            original_body: raw.body.clone(),
            translated_body: translation_result.translated_body,
            summary: translation_result.summary,
            category_name: raw.category_name,
            publish_time: raw.publish_time, // 保持原始UTC时间
            disclaimer: raw.disclaimer,
        };

        Ok(translated)
    }

    /// 调用AI API进行翻译和总结
    async fn translate_and_summarize(&self, title: &str, content: &str) -> Result<TranslationResult> {
        // 智能内容预处理：保留关键信息，移除冗余内容
        let content_preview = self.preprocess_content(content);

        // 限制内容长度，避免超出token限制，但优先保留重要信息
        let final_content = if content_preview.len() > 2000 {
            // 安全地截断字符串，避免UTF-8字符边界问题
            let safe_truncated = self.safe_truncate(&content_preview, 2000);
            // 尝试在句号或换行处截断，保持内容完整性
            if let Some(last_period) = safe_truncated.rfind('.') {
                &content_preview[..last_period + 1]
            } else if let Some(last_newline) = safe_truncated.rfind('\n') {
                &content_preview[..last_newline]
            } else {
                safe_truncated
            }
        } else {
            &content_preview
        };

        let prompt = format!(
            r#"你是一位专业的币安公告翻译专家。请对以下英文币安公告进行意译处理，确保中文表达自然流畅。

原文标题: {}
原文内容: {}

处理要求：
1. **意译处理**：进行准确的意译而非直译，确保中文表达自然流畅
2. **中文标题生成**：
   - 准确提炼并体现文章的核心主题
   - 简洁明了，突出最重要的关键信息（如活动名称、奖励金额、重要时间、上币通知、交易对等）
   - 优先级：上币通知 > 活动奖励 > 重要功能更新 > 一般公告
3. **中文概要生成**：
   - 完整概括文章的中心思想和重点内容
   - 必须包含关键信息：活动时间、参与方式、奖励详情、重要条件、截止时间等
   - 语言简洁明了，便于快速理解核心要点
   - 保持信息完整性，不要为了字数限制而省略重要信息

输出格式：严格按照以下JSON格式返回，不要添加markdown代码块或其他格式：
{{"translated_title": "中文标题", "translated_body": "完整翻译内容", "summary": "核心概要"}}"#,
            title, final_content
        );

        let response_text = self.call_ai_api(&prompt).await?;
        
        // 解析AI响应
        self.parse_translation_response(&response_text, title, content)
    }

    /// 调用 OpenRouter AI API 进行翻译
    async fn call_ai_api(&self, prompt: &str) -> Result<String> {
        println!("🔄 正在调用AI API进行翻译...");

        let payload = json!({
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一位专业的币安公告翻译专家，具有丰富的加密货币和区块链行业经验。你擅长将英文币安公告准确意译为自然流畅的中文，能够精准识别和突出关键信息。请严格按照用户要求的JSON格式返回结果，不要添加markdown代码块或其他格式。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.2,
            "max_tokens": 1200,
            "top_p": 0.9
        });

        println!("📡 发送请求到: {}/chat/completions", self.api_base_url);
        println!("🤖 使用模型: {}", self.model);

        let response = self.client
            .post(&format!("{}/chat/completions", self.api_base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .header("HTTP-Referer", "https://binance-watcher.local")
            .header("X-Title", "Binance Notice Watcher")
            .json(&payload)
            .timeout(std::time::Duration::from_secs(30))  // 添加30秒超时
            .send()
            .await?;

        println!("📨 收到API响应，状态码: {}", response.status());

        if !response.status().is_success() {
            let status = response.status();
            let error_text = response.text().await.unwrap_or_default();
            return Err(AppError::AiService(format!("OpenRouter API request failed: {} - {}", status, error_text)));
        }

        let response_json: serde_json::Value = response.json().await?;

        let content = response_json
            .get("choices")
            .and_then(|choices| choices.get(0))
            .and_then(|choice| choice.get("message"))
            .and_then(|message| message.get("content"))
            .and_then(|content| content.as_str())
            .ok_or_else(|| AppError::AiService("Invalid OpenRouter API response format".to_string()))?;

        Ok(content.to_string())
    }

    /// 解析AI翻译响应
    fn parse_translation_response(&self, response_text: &str, original_title: &str, original_content: &str) -> Result<TranslationResult> {
        // 使用改进的JSON解析器
        match ImprovedJsonParser::parse_ai_response(response_text) {
            Ok(parsed) => {
                println!("✅ JSON parsing successful with ImprovedJsonParser");

                let translated_title = parsed
                    .get("translated_title")
                    .and_then(|v| v.as_str())
                    .unwrap_or(original_title)
                    .to_string();

                let translated_body = parsed
                    .get("translated_body")
                    .and_then(|v| v.as_str())
                    .unwrap_or(original_content)
                    .to_string();

                let summary = parsed
                    .get("summary")
                    .and_then(|v| v.as_str())
                    .unwrap_or("无法生成概要")
                    .to_string();

                Ok(TranslationResult {
                    translated_title,
                    translated_body,
                    summary,
                })
            }
            Err(e) => {
                eprintln!("❌ ImprovedJsonParser failed: {}", e);
                eprintln!("❌ Raw response: {}", response_text);

                // 降级处理：返回原始内容
                let safe_title = if response_text.len() > 50 {
                    // 安全截断
                    let mut end = 50;
                    while end > 0 && !response_text.is_char_boundary(end) {
                        end -= 1;
                    }
                    format!("{}…", &response_text[..end])
                } else {
                    response_text.to_string()
                };

                Ok(TranslationResult {
                    translated_title: safe_title,
                    translated_body: original_content.to_string(),
                    summary: "AI翻译服务暂时不可用，请稍后重试".to_string(),
                })
            }
        }
    }



    /// 安全地截断字符串，避免UTF-8字符边界问题
    fn safe_truncate<'a>(&self, s: &'a str, max_bytes: usize) -> &'a str {
        if s.len() <= max_bytes {
            return s;
        }

        // 从目标位置向前查找，找到有效的UTF-8字符边界
        let mut end = max_bytes;
        while end > 0 && !s.is_char_boundary(end) {
            end -= 1;
        }

        &s[..end]
    }

    /// 智能内容预处理：保留关键信息，移除冗余内容
    fn preprocess_content(&self, content: &str) -> String {
        let mut processed = content.to_string();

        // 移除常见的冗余信息，但保留重要内容
        let redundant_patterns = [
            "This is a general announcement. Products and services referred to here may not be available in your region.",
            "Fellow Binancians,",
            "Thank you for your support!",
            "Binance Team",
            "Find us on Telegram",
            "WhatsApp",
            "Facebook",
            "Instagram",
            "Discord",
            "Trade on-the-go with Binance's crypto trading app",
            "Binance reserves the right in its sole discretion to amend or cancel this announcement",
            "Disclaimer: All content on Binance Square is presented on an \"as is\" basis",
            "No content shall be construed as financial advice",
        ];

        for pattern in &redundant_patterns {
            processed = processed.replace(pattern, "");
        }

        // 清理多余的空行和空格
        processed = processed
            .lines()
            .map(|line| line.trim())
            .filter(|line| !line.is_empty())
            .collect::<Vec<_>>()
            .join("\n");

        // 保留重要的结构化信息
        processed = processed.replace("Activity Period:", "\n活动时间:");
        processed = processed.replace("How to Participate:", "\n参与方式:");
        processed = processed.replace("Rewards Structure:", "\n奖励结构:");
        processed = processed.replace("Terms and Conditions:", "\n条款条件:");

        processed
    }

    /// 格式化时间为 UTC+8 格式，包含毫秒精度
    pub fn format_time_utc8_with_millis(utc_time: &DateTime<Utc>) -> String {
        let utc8_offset = FixedOffset::east_opt(8 * 3600).unwrap(); // UTC+8
        let local_time = utc_time.with_timezone(&utc8_offset);
        local_time.format("%Y-%m-%d %H:%M:%S%.3f").to_string()
    }

    /// 格式化时间为 UTC+8 格式（保留原函数以兼容）
    #[allow(dead_code)]
    pub fn format_time_utc8(utc_time: &DateTime<Utc>) -> String {
        let utc8_offset = FixedOffset::east_opt(8 * 3600).unwrap(); // UTC+8
        let local_time = utc_time.with_timezone(&utc8_offset);
        local_time.format("%Y-%m-%d %H:%M:%S").to_string()
    }


}
