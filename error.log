nohup: ignoring input
❌ ImprovedJsonParser failed: JSON解析失败: expected `,` or `}` at line 3 column 440
❌ Raw response: {
    "translated_title": "币安联盟独家空投：Treehouse (TREE) 代币派发，总量10万枚等你瓜分！",
    "translated_body": "为感谢币安联盟合作伙伴的持续支持，币安隆重推出一场限时独家代币空投活动。诚邀朋友注册、充值并交易，即可获得Treehouse (TREE)空投奖励。

注册入口已开启
活动时间：2025年8月4日09:00 (UTC) 至 2025年8月10日23:59 (UTC)

活动A：币安联盟专属 - 邀请好友共享4万TREE

参与步骤：
1. 币安联盟必须在活动页面点击"立即注册"确认参与
2. 将专属邀请链接分享给朋友，邀请其注册币安
3. 邀请至少3名有效新用户，即可瓜分4万TREE奖池

有效新用户定义：
- 通过联盟成员邀请链接注册并完成身份验证
- 在活动期间累计充值不少于50美元或等值资产
- 在币安现货交易累计交易额不少于100美元或等值资产

奖励分配规则：
- 按照邀请有效新用户数量按比例分配奖励
- 邀请用户越多，获得奖励越多
- 仅统计联盟成员确认参与后的新用户注册

活动B：新用户专属 - 完成任务共享6万TREE空投

参与步骤：
1. 通过币安联盟成员邀请链接注册并完成身份验证
2. 在活动期间完成充值和交易要求：
   - 累计充值不少于50美元或等值资产
   - 在币安现货交易累计交易额不少于100美元或等值资产",
    "summary": "币安推出Treehouse (TREE)代币限时空投活动，总量10万枚。活动分为两个部分：币安联盟可邀请好友瓜分4万TREE，新用户完成充值交易可获得6万TREE。活动时间为2025年8月4日至10日，鼓励用户注册、充值和交易。"
}
Failed to send ping: WebSocket protocol error: Sending after closing is not allowed
❌ ImprovedJsonParser failed: JSON解析失败: expected `,` or `}` at line 3 column 62
❌ Raw response: {
    "translated_title": "币安Learn & Earn：完成测验瓜分3.3万枚NEAR代币奖励！",
    "translated_body": "币安很高兴宣布推出下一轮"币安Learn & Earn"活动，用户可以通过完成指定测验来学习区块链知识并获得加密货币奖励。

活动时间：2025年8月5日09:00 (UTC) 至 2025年8月19日09:00 (UTC)

参与条件：
仅限2024年8月5日09:00 (UTC)之前从未订阅过定期理财产品的新用户，可按先到先得原则获得预设数量的NEAR代币奖励。用户可即日起阅读文章和观看视频，并在代币供应持续期间完成测验。每个Learn & Earn活动仅可完成一次，每位用户每个活动最多获得一次奖励。

重要提示：
- 所有奖励发放完毕后将不再接受参与
- NEAR奖励将自动锁定在定期理财产品中150天，年化收益为10%

奖励产品详情：
数字资产 | 本金奖励 | 锁仓期限 | 标准年化收益
NEAR | 0.3 NEAR | 150天 | 10%

持续关注更多项目和加密货币奖励机会！

参与条件细则：
- 新用户必须完成身份验证(KYC)
- 禁止批量注册或使用子账户
- 奖励数量有限，按先到先得原则分配
- 用户需完成相应测验后方可领取奖励
- 符合条件的用户可参与多个Learn & Earn活动",
    "summary": "币安推出Learn & Earn活动，面向新用户提供3.3万枚NEAR代币奖励。活动时间为2025年8月5日至19日，仅限未订阅定期理财的新用户参与。奖励0.3 NEAR，将自动锁仓150天，年化收益10%。用户需完成KYC和指定测验，先到先得。"
}
