{"rustc": 1842507548689473721, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 4067574213046180398, "path": 12636550273965699417, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\allocator-api2-50dd6e206a3a1bde\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}